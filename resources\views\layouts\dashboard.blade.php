<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'SIMAK') }} - @yield('title', 'Dashboard')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        /* Custom purple-pink gradient theme */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .gradient-purple-pink {
            background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            background: linear-gradient(135deg, #9333ea 0%, #db2777 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(168, 85, 247, 0.3);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-gradient {
            background: linear-gradient(180deg, #1e1b4b 0%, #581c87 100%);
        }
        
        .nav-item {
            transition: all 0.3s ease;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
        }
        
        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
        }
        
        .notification-dot {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #9333ea 0%, #db2777 100%);
        }
        
        /* Mobile sidebar */
        .sidebar-mobile {
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .sidebar-mobile.open {
            transform: translateX(0);
        }
    </style>
    
    @stack('styles')
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar-mobile lg:translate-x-0 fixed inset-y-0 left-0 z-50 w-64 sidebar-gradient lg:static lg:inset-0">
            <div class="flex items-center justify-center h-16 px-4 border-b border-white/10">
                <div class="flex items-center">
                    <div class="h-10 w-10 bg-white rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">SIMAK</h1>
                        <p class="text-xs text-purple-200">{{ auth()->user()->role === 'dosen' ? 'Dosen' : 'Mahasiswa' }}</p>
                    </div>
                </div>
            </div>
            
            <nav class="mt-8 px-4">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="{{ auth()->user()->isDosen() ? route('dashboard.dosen') : route('dashboard.mahasiswa') }}"
                       class="nav-item flex items-center px-4 py-3 text-white hover:text-purple-200 {{ request()->routeIs('dashboard.*') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        <span>Dashboard</span>
                    </a>
                    
                    <!-- Materi -->
                    <a href="{{ route('materials.index') }}" class="nav-item flex items-center px-4 py-3 text-white hover:text-purple-200 {{ request()->routeIs('materials.*') ? 'active' : '' }}">
                        <i class="fas fa-book mr-3"></i>
                        <span>Materi Kuliah</span>
                    </a>

                    <!-- Jadwal -->
                    <a href="{{ route('schedules.index') }}" class="nav-item flex items-center px-4 py-3 text-white hover:text-purple-200 {{ request()->routeIs('schedules.*') ? 'active' : '' }}">
                        <i class="fas fa-calendar-alt mr-3"></i>
                        <span>Jadwal</span>
                    </a>
                    
                    @if(auth()->user()->isDosen())
                    <!-- Upload Materi (Dosen only) -->
                    <a href="{{ route('materials.create') }}" class="nav-item flex items-center px-4 py-3 text-white hover:text-purple-200">
                        <i class="fas fa-upload mr-3"></i>
                        <span>Upload Materi</span>
                    </a>

                    <!-- Kelola Jadwal (Dosen only) -->
                    <a href="{{ route('schedules.create') }}" class="nav-item flex items-center px-4 py-3 text-white hover:text-purple-200">
                        <i class="fas fa-cog mr-3"></i>
                        <span>Kelola Jadwal</span>
                    </a>
                    @endif
                    
                    <!-- Notifikasi -->
                    <a href="#" class="nav-item flex items-center px-4 py-3 text-white hover:text-purple-200">
                        <i class="fas fa-bell mr-3"></i>
                        <span>Notifikasi</span>
                        <span class="ml-auto bg-pink-500 text-white text-xs rounded-full px-2 py-1 notification-dot">3</span>
                    </a>
                </div>
                
                <!-- User Section -->
                <div class="mt-8 pt-8 border-t border-white/10">
                    <div class="px-4 py-3">
                        <div class="flex items-center">
                            <div class="h-10 w-10 bg-white rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-purple-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-white">{{ auth()->user()->name }}</p>
                                <p class="text-xs text-purple-200">{{ auth()->user()->email }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-4 space-y-2">
                        <a href="#" class="nav-item flex items-center px-4 py-2 text-white hover:text-purple-200">
                            <i class="fas fa-user-cog mr-3"></i>
                            <span>Profil</span>
                        </a>
                        
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="nav-item w-full flex items-center px-4 py-2 text-white hover:text-purple-200">
                                <i class="fas fa-sign-out-alt mr-3"></i>
                                <span>Logout</span>
                            </button>
                        </form>
                    </div>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button id="sidebar-toggle" class="lg:hidden text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="ml-4 text-xl font-semibold text-gray-800">@yield('page-title', 'Dashboard')</h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Search -->
                        <div class="relative">
                            <input type="text" placeholder="Cari materi..." 
                                   class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                        
                        <!-- Notifications -->
                        <button class="relative p-2 text-gray-500 hover:text-gray-700 focus:outline-none">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full notification-dot"></span>
                        </button>
                        
                        <!-- User Avatar -->
                        <div class="h-8 w-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Page Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
                @yield('content')
            </main>
        </div>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden"></div>
    
    <script>
        // Mobile sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
            sidebarOverlay.classList.toggle('hidden');
        });
        
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('open');
            sidebarOverlay.classList.add('hidden');
        });
    </script>
    
    @stack('scripts')
</body>
</html>
