<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Material;
use App\Models\Schedule;
use App\Models\Subject;
use App\Models\Notification;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function mahasiswa()
    {
        $user = Auth::user();

        // Get statistics
        $totalMaterials = Material::whereHas('subject', function($query) use ($user) {
            $query->where('jurusan', $user->jurusan);
        })->count();

        $todaySchedules = Schedule::whereHas('subject', function($query) use ($user) {
            $query->where('jurusan', $user->jurusan);
        })->where('hari', now()->locale('id')->dayName)->get();

        $recentMaterials = Material::whereHas('subject', function($query) use ($user) {
            $query->where('jurusan', $user->jurusan);
        })->latest()->take(5)->get();

        $notifications = $user->notifications()->latest()->take(5)->get();

        return view('dashboard.mahasiswa', compact(
            'user', 'totalMaterials', 'todaySchedules', 'recentMaterials', 'notifications'
        ));
    }

    public function dosen()
    {
        $user = Auth::user();

        // Get statistics
        $totalMaterials = $user->uploadedMaterials()->count();
        $totalSubjects = Subject::where('jurusan', $user->jurusan)->count();
        $todaySchedules = $user->schedules()->where('hari', now()->locale('id')->dayName)->get();

        $recentMaterials = $user->uploadedMaterials()->latest()->take(5)->get();
        $notifications = $user->notifications()->latest()->take(5)->get();

        return view('dashboard.dosen', compact(
            'user', 'totalMaterials', 'totalSubjects', 'todaySchedules', 'recentMaterials', 'notifications'
        ));
    }
}
