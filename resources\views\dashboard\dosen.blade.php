@extends('layouts.dashboard')

@section('title', 'Dashboard Dosen')
@section('page-title', 'Dashboard Dosen')

@section('content')
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Selamat datang, {{ $user->name }}! 👨‍🏫</h1>
                <p class="text-gray-600 mt-1">{{ $user->jurusan }} - {{ $user->nim_nip }}</p>
                <p class="text-sm text-gray-500 mt-2">{{ now()->locale('id')->isoFormat('dddd, D MMMM Y') }}</p>
            </div>
            <div class="hidden md:block">
                <div class="h-20 w-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Materi -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 card-hover">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-book text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Materi Diupload</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $totalMaterials }}</p>
                </div>
            </div>
        </div>

        <!-- Total Mata Kuliah -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 card-hover">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Mata Kuliah</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $totalSubjects }}</p>
                </div>
            </div>
        </div>

        <!-- Jadwal Hari Ini -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 card-hover">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-day text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Jadwal Hari Ini</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $todaySchedules->count() }}</p>
                </div>
            </div>
        </div>

        <!-- Notifikasi -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 card-hover">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bell text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Notifikasi</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $notifications->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Jadwal Mengajar Hari Ini -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-calendar-day text-purple-600 mr-2"></i>
                        Jadwal Mengajar Hari Ini
                    </h3>
                    <span class="text-sm text-gray-500">{{ now()->locale('id')->isoFormat('dddd') }}</span>
                </div>
            </div>
            <div class="p-6">
                @if($todaySchedules->count() > 0)
                    <div class="space-y-4">
                        @foreach($todaySchedules as $schedule)
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <div class="h-12 w-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="font-medium text-gray-900">{{ $schedule->subject->nama_mk }}</h4>
                                <p class="text-sm text-gray-600">{{ $schedule->subject->kode_mk }}</p>
                                <p class="text-sm text-gray-500">
                                    {{ $schedule->jam_mulai }} - {{ $schedule->jam_selesai }} | {{ $schedule->ruang }}
                                </p>
                            </div>
                            <div class="ml-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ $schedule->subject->sks }} SKS
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">Tidak ada jadwal mengajar hari ini</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Materi Terbaru -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-book text-purple-600 mr-2"></i>
                        Materi Terbaru Diupload
                    </h3>
                    <a href="#" class="text-sm text-purple-600 hover:text-purple-700 font-medium">Lihat Semua</a>
                </div>
            </div>
            <div class="p-6">
                @if($recentMaterials->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentMaterials as $material)
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                            <div class="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                @if($material->type === 'pdf')
                                    <i class="fas fa-file-pdf text-red-600"></i>
                                @elseif($material->type === 'link')
                                    <i class="fas fa-link text-blue-600"></i>
                                @else
                                    <i class="fas fa-file-alt text-gray-600"></i>
                                @endif
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="font-medium text-gray-900">{{ $material->title }}</h4>
                                <p class="text-sm text-gray-600">{{ $material->subject->nama_mk }}</p>
                                <p class="text-xs text-gray-500">{{ $material->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="ml-4 flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    @if($material->type === 'pdf') bg-red-100 text-red-800
                                    @elseif($material->type === 'link') bg-blue-100 text-blue-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ strtoupper($material->type) }}
                                </span>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-upload text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">Belum ada materi yang diupload</p>
                        <button class="mt-4 btn-gradient text-white px-4 py-2 rounded-lg text-sm">
                            Upload Materi Pertama
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions for Dosen -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-bolt text-purple-600 mr-2"></i>
            Aksi Cepat
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="#" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-200">
                <div class="h-12 w-12 bg-purple-500 rounded-lg flex items-center justify-center mb-3">
                    <i class="fas fa-upload text-white"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Upload Materi</span>
            </a>
            
            <a href="#" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-200">
                <div class="h-12 w-12 bg-blue-500 rounded-lg flex items-center justify-center mb-3">
                    <i class="fas fa-calendar-plus text-white"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Kelola Jadwal</span>
            </a>
            
            <a href="#" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition duration-200">
                <div class="h-12 w-12 bg-green-500 rounded-lg flex items-center justify-center mb-3">
                    <i class="fas fa-comments text-white"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Lihat Diskusi</span>
            </a>
            
            <a href="#" class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition duration-200">
                <div class="h-12 w-12 bg-yellow-500 rounded-lg flex items-center justify-center mb-3">
                    <i class="fas fa-chart-bar text-white"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Statistik</span>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-history text-purple-600 mr-2"></i>
                Aktivitas Terbaru
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center p-4 bg-blue-50 rounded-lg">
                    <div class="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-upload text-white text-sm"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-900">Materi "Pengenalan Laravel" berhasil diupload</p>
                        <p class="text-xs text-gray-500">2 jam yang lalu</p>
                    </div>
                </div>
                
                <div class="flex items-center p-4 bg-green-50 rounded-lg">
                    <div class="h-10 w-10 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-comment text-white text-sm"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-900">3 komentar baru pada materi "Basis Data"</p>
                        <p class="text-xs text-gray-500">5 jam yang lalu</p>
                    </div>
                </div>
                
                <div class="flex items-center p-4 bg-yellow-50 rounded-lg">
                    <div class="h-10 w-10 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar text-white text-sm"></i>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-900">Jadwal kuliah "Algoritma" diperbarui</p>
                        <p class="text-xs text-gray-500">1 hari yang lalu</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
