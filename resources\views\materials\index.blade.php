@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON>')
@section('page-title', '<PERSON><PERSON>')

@section('content')
<div class="space-y-6">
    <!-- Header Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON></h1>
                <p class="text-gray-600 mt-1">Akses dan kelola materi pembelajaran</p>
            </div>
            @if(auth()->user()->isDosen())
            <div class="mt-4 md:mt-0">
                <a href="{{ route('materials.create') }}" 
                   class="btn-gradient text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    Upload Materi
                </a>
            </div>
            @endif
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <form method="GET" action="{{ route('materials.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
            <!-- Search -->
            <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Cari Materi</label>
                <div class="relative">
                    <input type="text" name="search" id="search" 
                           value="{{ request('search') }}"
                           placeholder="Cari berdasarkan judul, deskripsi, atau mata kuliah..."
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Subject Filter -->
            <div class="md:w-64">
                <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-2">Mata Kuliah</label>
                <select name="subject_id" id="subject_id" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="">Semua Mata Kuliah</option>
                    @foreach($subjects as $subject)
                        <option value="{{ $subject->id }}" {{ request('subject_id') == $subject->id ? 'selected' : '' }}>
                            {{ $subject->nama_mk }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Type Filter -->
            <div class="md:w-48">
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Tipe</label>
                <select name="type" id="type" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="">Semua Tipe</option>
                    <option value="pdf" {{ request('type') == 'pdf' ? 'selected' : '' }}>PDF</option>
                    <option value="text" {{ request('type') == 'text' ? 'selected' : '' }}>Text</option>
                    <option value="link" {{ request('type') == 'link' ? 'selected' : '' }}>Link</option>
                </select>
            </div>

            <!-- Filter Button -->
            <div>
                <button type="submit" 
                        class="w-full md:w-auto px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>
            </div>

            <!-- Reset Button -->
            @if(request()->hasAny(['search', 'subject_id', 'type']))
            <div>
                <a href="{{ route('materials.index') }}" 
                   class="w-full md:w-auto px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition duration-200 inline-block text-center">
                    <i class="fas fa-times mr-2"></i>
                    Reset
                </a>
            </div>
            @endif
        </form>
    </div>

    <!-- Materials Grid -->
    @if($materials->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($materials as $material)
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 card-hover">
            <!-- Material Header -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="h-10 w-10 rounded-lg flex items-center justify-center mr-3
                                @if($material->type === 'pdf') bg-red-100
                                @elseif($material->type === 'link') bg-blue-100
                                @else bg-gray-100 @endif">
                                @if($material->type === 'pdf')
                                    <i class="fas fa-file-pdf text-red-600"></i>
                                @elseif($material->type === 'link')
                                    <i class="fas fa-link text-blue-600"></i>
                                @else
                                    <i class="fas fa-file-alt text-gray-600"></i>
                                @endif
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($material->type === 'pdf') bg-red-100 text-red-800
                                @elseif($material->type === 'link') bg-blue-100 text-blue-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ strtoupper($material->type) }}
                            </span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $material->title }}</h3>
                        <p class="text-sm text-gray-600 mb-3">{{ Str::limit($material->description, 100) }}</p>
                    </div>
                </div>
            </div>

            <!-- Material Info -->
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-graduation-cap mr-2 text-purple-500"></i>
                        <span>{{ $material->subject->nama_mk }}</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-user mr-2 text-purple-500"></i>
                        <span>{{ $material->uploader->name }}</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-clock mr-2 text-purple-500"></i>
                        <span>{{ $material->created_at->diffForHumans() }}</span>
                    </div>
                    @if($material->type === 'pdf' && $material->file_size)
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-file-alt mr-2 text-purple-500"></i>
                        <span>{{ number_format($material->file_size / 1024, 2) }} KB</span>
                    </div>
                    @endif
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 flex items-center justify-between">
                    <a href="{{ route('materials.show', $material) }}" 
                       class="text-purple-600 hover:text-purple-700 font-medium text-sm">
                        <i class="fas fa-eye mr-1"></i>
                        Lihat Detail
                    </a>
                    
                    @if(auth()->user()->id === $material->uploaded_by)
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('materials.edit', $material) }}" 
                           class="text-blue-600 hover:text-blue-700 text-sm">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form method="POST" action="{{ route('materials.destroy', $material) }}" 
                              onsubmit="return confirm('Yakin ingin menghapus materi ini?')" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:text-red-700 text-sm">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        {{ $materials->links() }}
    </div>
    @else
    <!-- Empty State -->
    <div class="bg-white rounded-xl shadow-sm p-12 border border-gray-200 text-center">
        <div class="max-w-md mx-auto">
            <div class="h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-book-open text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Materi</h3>
            <p class="text-gray-600 mb-6">
                @if(request()->hasAny(['search', 'subject_id', 'type']))
                    Tidak ada materi yang sesuai dengan filter yang dipilih.
                @else
                    Belum ada materi yang tersedia untuk jurusan Anda.
                @endif
            </p>
            @if(auth()->user()->isDosen())
            <a href="{{ route('materials.create') }}" 
               class="btn-gradient text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                <i class="fas fa-plus mr-2"></i>
                Upload Materi Pertama
            </a>
            @endif
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div id="success-alert" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
    <div class="flex items-center">
        <i class="fas fa-check-circle mr-2"></i>
        {{ session('success') }}
    </div>
</div>

<script>
    setTimeout(function() {
        const alert = document.getElementById('success-alert');
        if (alert) {
            alert.style.opacity = '0';
            alert.style.transform = 'translateX(100%)';
            setTimeout(() => alert.remove(), 300);
        }
    }, 3000);
</script>
@endif
@endsection
