<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\Material;
use App\Models\Subject;
use App\Models\Comment;

class MaterialController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Material::with(['subject', 'uploader'])
            ->where('is_active', true);

        // Filter by subject if user is mahasiswa
        if (Auth::user()->isMahasiswa()) {
            $query->whereHas('subject', function($q) {
                $q->where('jurusan', Auth::user()->jurusan);
            });
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('subject', function($subQuery) use ($search) {
                      $subQuery->where('nama_mk', 'like', "%{$search}%")
                               ->orWhere('kode_mk', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by subject
        if ($request->has('subject_id') && $request->subject_id) {
            $query->where('subject_id', $request->subject_id);
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        $materials = $query->latest()->paginate(12);

        // Get subjects for filter dropdown
        $subjects = Subject::where('jurusan', Auth::user()->jurusan)->get();

        return view('materials.index', compact('materials', 'subjects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Only dosen can create materials
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $subjects = Subject::where('jurusan', Auth::user()->jurusan)->get();
        return view('materials.create', compact('subjects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Only dosen can store materials
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'subject_id' => 'required|exists:subjects,id',
            'type' => 'required|in:pdf,text,link',
            'content' => 'required_if:type,text',
            'link_url' => 'required_if:type,link|url',
            'file' => 'required_if:type,pdf|file|mimes:pdf|max:10240', // 10MB max
        ]);

        $material = new Material();
        $material->title = $request->title;
        $material->description = $request->description;
        $material->subject_id = $request->subject_id;
        $material->uploaded_by = Auth::id();
        $material->type = $request->type;

        if ($request->type === 'text') {
            $material->content = $request->content;
        } elseif ($request->type === 'link') {
            $material->link_url = $request->link_url;
        } elseif ($request->type === 'pdf' && $request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('materials', $filename, 'public');

            $material->file_path = $path;
            $material->file_name = $file->getClientOriginalName();
            $material->file_size = $file->getSize();
        }

        $material->save();

        return redirect()->route('materials.index')
            ->with('success', 'Materi berhasil diupload!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Material $material)
    {
        // Check if user can access this material
        if (Auth::user()->isMahasiswa() && $material->subject->jurusan !== Auth::user()->jurusan) {
            abort(403, 'Unauthorized action.');
        }

        $material->load(['subject', 'uploader', 'comments.user', 'comments.replies.user']);

        return view('materials.show', compact('material'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Material $material)
    {
        // Only the uploader can edit
        if ($material->uploaded_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $subjects = Subject::where('jurusan', Auth::user()->jurusan)->get();
        return view('materials.edit', compact('material', 'subjects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Material $material)
    {
        // Only the uploader can update
        if ($material->uploaded_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'subject_id' => 'required|exists:subjects,id',
            'type' => 'required|in:pdf,text,link',
            'content' => 'required_if:type,text',
            'link_url' => 'required_if:type,link|url',
            'file' => 'nullable|file|mimes:pdf|max:10240', // 10MB max
        ]);

        $material->title = $request->title;
        $material->description = $request->description;
        $material->subject_id = $request->subject_id;
        $material->type = $request->type;

        if ($request->type === 'text') {
            $material->content = $request->content;
            $material->link_url = null;
        } elseif ($request->type === 'link') {
            $material->link_url = $request->link_url;
            $material->content = null;
        } elseif ($request->type === 'pdf') {
            $material->content = null;
            $material->link_url = null;

            if ($request->hasFile('file')) {
                // Delete old file if exists
                if ($material->file_path) {
                    Storage::disk('public')->delete($material->file_path);
                }

                $file = $request->file('file');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('materials', $filename, 'public');

                $material->file_path = $path;
                $material->file_name = $file->getClientOriginalName();
                $material->file_size = $file->getSize();
            }
        }

        $material->save();

        return redirect()->route('materials.show', $material)
            ->with('success', 'Materi berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Material $material)
    {
        // Only the uploader can delete
        if ($material->uploaded_by !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Delete file if exists
        if ($material->file_path) {
            Storage::disk('public')->delete($material->file_path);
        }

        $material->delete();

        return redirect()->route('materials.index')
            ->with('success', 'Materi berhasil dihapus!');
    }
}
