<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Subject;
use App\Models\Schedule;
use App\Models\Material;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create sample users
        $dosen1 = User::create([
            'name' => 'Dr. Ahmad Wijaya',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'dosen',
            'nim_nip' => '198501012010011001',
            'jurusan' => 'Teknik Informatika',
            'phone' => '081234567890',
        ]);

        $dosen2 = User::create([
            'name' => 'Prof. Siti Nurhaliza',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'dosen',
            'nim_nip' => '197803152005012002',
            'jurusan' => 'Sistem Informasi',
            'phone' => '081234567891',
        ]);

        $mahasiswa1 = User::create([
            'name' => 'Budi Santoso',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'mahasiswa',
            'nim_nip' => '2021001001',
            'jurusan' => 'Teknik Informatika',
            'phone' => '081234567892',
        ]);

        $mahasiswa2 = User::create([
            'name' => 'Andi Pratama',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'mahasiswa',
            'nim_nip' => '2021002001',
            'jurusan' => 'Sistem Informasi',
            'phone' => '081234567893',
        ]);

        // Create sample subjects
        $subject1 = Subject::create([
            'kode_mk' => 'TI001',
            'nama_mk' => 'Pemrograman Web',
            'jurusan' => 'Teknik Informatika',
            'tahun_akademik' => '2024/2025',
            'semester' => '3',
            'sks' => 3,
            'deskripsi' => 'Mata kuliah yang membahas tentang pengembangan aplikasi web menggunakan teknologi modern.',
        ]);

        $subject2 = Subject::create([
            'kode_mk' => 'SI001',
            'nama_mk' => 'Basis Data',
            'jurusan' => 'Sistem Informasi',
            'tahun_akademik' => '2024/2025',
            'semester' => '2',
            'sks' => 3,
            'deskripsi' => 'Mata kuliah yang membahas konsep dan implementasi basis data.',
        ]);

        $subject3 = Subject::create([
            'kode_mk' => 'TI002',
            'nama_mk' => 'Algoritma dan Struktur Data',
            'jurusan' => 'Teknik Informatika',
            'tahun_akademik' => '2024/2025',
            'semester' => '2',
            'sks' => 4,
            'deskripsi' => 'Mata kuliah yang membahas algoritma dan struktur data fundamental.',
        ]);

        // Create sample schedules
        Schedule::create([
            'subject_id' => $subject1->id,
            'dosen_id' => $dosen1->id,
            'ruang' => 'Lab Komputer 1',
            'hari' => 'Senin',
            'jam_mulai' => '08:00',
            'jam_selesai' => '10:30',
        ]);

        Schedule::create([
            'subject_id' => $subject2->id,
            'dosen_id' => $dosen2->id,
            'ruang' => 'Ruang 201',
            'hari' => 'Selasa',
            'jam_mulai' => '10:30',
            'jam_selesai' => '12:00',
        ]);

        Schedule::create([
            'subject_id' => $subject3->id,
            'dosen_id' => $dosen1->id,
            'ruang' => 'Ruang 301',
            'hari' => 'Rabu',
            'jam_mulai' => '13:00',
            'jam_selesai' => '15:30',
        ]);

        // Create sample materials
        Material::create([
            'title' => 'Pengenalan HTML dan CSS',
            'description' => 'Materi dasar tentang HTML dan CSS untuk pengembangan web.',
            'subject_id' => $subject1->id,
            'uploaded_by' => $dosen1->id,
            'type' => 'text',
            'content' => 'HTML (HyperText Markup Language) adalah bahasa markup standar untuk membuat halaman web...',
        ]);

        Material::create([
            'title' => 'Tutorial Laravel Framework',
            'description' => 'Panduan lengkap menggunakan Laravel untuk pengembangan web.',
            'subject_id' => $subject1->id,
            'uploaded_by' => $dosen1->id,
            'type' => 'link',
            'link_url' => 'https://laravel.com/docs',
        ]);

        Material::create([
            'title' => 'Konsep Basis Data Relasional',
            'description' => 'Penjelasan tentang konsep dasar basis data relasional.',
            'subject_id' => $subject2->id,
            'uploaded_by' => $dosen2->id,
            'type' => 'text',
            'content' => 'Basis data relasional adalah jenis basis data yang mengorganisir data dalam bentuk tabel...',
        ]);
    }
}
