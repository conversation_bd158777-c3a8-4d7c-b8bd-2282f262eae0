<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Subject extends Model
{
    use HasFactory;

    protected $fillable = [
        'kode_mk',
        'nama_mk',
        'jurusan',
        'tahun_akademik',
        'semester',
        'sks',
        'deskripsi',
    ];

    // Relationships
    public function materials()
    {
        return $this->hasMany(Material::class);
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }
}
