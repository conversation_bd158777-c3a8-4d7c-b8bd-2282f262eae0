@extends('layouts.dashboard')

@section('title', 'Upload Materi')
@section('page-title', 'Upload Materi Baru')

@section('content')
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Upload Materi Baru</h1>
                <p class="text-gray-600 mt-1">Tambahkan materi pembelajaran untuk mahasiswa</p>
            </div>
            <a href="{{ route('materials.index') }}" 
               class="text-gray-600 hover:text-gray-800 font-medium">
                <i class="fas fa-arrow-left mr-2"></i>
                Kembali
            </a>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <form method="POST" action="{{ route('materials.store') }}" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf
            
            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-heading text-purple-500 mr-2"></i>
                    Judul Materi *
                </label>
                <input type="text" name="title" id="title" required
                       value="{{ old('title') }}"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent @error('title') border-red-500 @enderror"
                       placeholder="Masukkan judul materi">
                @error('title')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-align-left text-purple-500 mr-2"></i>
                    Deskripsi *
                </label>
                <textarea name="description" id="description" rows="4" required
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent @error('description') border-red-500 @enderror"
                          placeholder="Jelaskan tentang materi ini...">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Subject -->
            <div>
                <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-graduation-cap text-purple-500 mr-2"></i>
                    Mata Kuliah *
                </label>
                <select name="subject_id" id="subject_id" required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent @error('subject_id') border-red-500 @enderror">
                    <option value="">Pilih Mata Kuliah</option>
                    @foreach($subjects as $subject)
                        <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                            {{ $subject->kode_mk }} - {{ $subject->nama_mk }}
                        </option>
                    @endforeach
                </select>
                @error('subject_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Material Type -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-4">
                    <i class="fas fa-file text-purple-500 mr-2"></i>
                    Tipe Materi *
                </label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- PDF Option -->
                    <div class="relative">
                        <input type="radio" name="type" id="type_pdf" value="pdf" 
                               class="sr-only" {{ old('type') == 'pdf' ? 'checked' : '' }}>
                        <label for="type_pdf" class="material-type-option flex flex-col items-center p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300 transition duration-200">
                            <div class="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-file-pdf text-red-600 text-xl"></i>
                            </div>
                            <span class="font-medium text-gray-900">File PDF</span>
                            <span class="text-sm text-gray-500 text-center mt-1">Upload file PDF (max 10MB)</span>
                        </label>
                    </div>

                    <!-- Text Option -->
                    <div class="relative">
                        <input type="radio" name="type" id="type_text" value="text" 
                               class="sr-only" {{ old('type') == 'text' ? 'checked' : '' }}>
                        <label for="type_text" class="material-type-option flex flex-col items-center p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300 transition duration-200">
                            <div class="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-file-alt text-gray-600 text-xl"></i>
                            </div>
                            <span class="font-medium text-gray-900">Teks</span>
                            <span class="text-sm text-gray-500 text-center mt-1">Tulis konten langsung</span>
                        </label>
                    </div>

                    <!-- Link Option -->
                    <div class="relative">
                        <input type="radio" name="type" id="type_link" value="link" 
                               class="sr-only" {{ old('type') == 'link' ? 'checked' : '' }}>
                        <label for="type_link" class="material-type-option flex flex-col items-center p-6 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300 transition duration-200">
                            <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-link text-blue-600 text-xl"></i>
                            </div>
                            <span class="font-medium text-gray-900">Link</span>
                            <span class="text-sm text-gray-500 text-center mt-1">Link ke sumber eksternal</span>
                        </label>
                    </div>
                </div>
                @error('type')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- PDF Upload -->
            <div id="pdf_section" class="material-section hidden">
                <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-upload text-purple-500 mr-2"></i>
                    Upload File PDF *
                </label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition duration-200">
                    <input type="file" name="file" id="file" accept=".pdf" 
                           class="hidden" onchange="updateFileName(this)">
                    <label for="file" class="cursor-pointer">
                        <div class="space-y-2">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl"></i>
                            <div class="text-gray-600">
                                <span class="font-medium text-purple-600">Klik untuk upload</span> atau drag & drop
                            </div>
                            <div class="text-sm text-gray-500">PDF hingga 10MB</div>
                        </div>
                    </label>
                    <div id="file-name" class="mt-2 text-sm text-gray-600 hidden"></div>
                </div>
                @error('file')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Text Content -->
            <div id="text_section" class="material-section hidden">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-edit text-purple-500 mr-2"></i>
                    Konten Materi *
                </label>
                <textarea name="content" id="content" rows="10"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent @error('content') border-red-500 @enderror"
                          placeholder="Tulis konten materi di sini...">{{ old('content') }}</textarea>
                @error('content')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Link URL -->
            <div id="link_section" class="material-section hidden">
                <label for="link_url" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-link text-purple-500 mr-2"></i>
                    URL Link *
                </label>
                <input type="url" name="link_url" id="link_url"
                       value="{{ old('link_url') }}"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent @error('link_url') border-red-500 @enderror"
                       placeholder="https://example.com">
                @error('link_url')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('materials.index') }}" 
                   class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200">
                    Batal
                </a>
                <button type="submit" 
                        class="btn-gradient text-white px-8 py-3 rounded-lg font-medium">
                    <i class="fas fa-save mr-2"></i>
                    Upload Materi
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.material-type-option input[type="radio"]:checked + label {
    border-color: #a855f7;
    background-color: #faf5ff;
}
</style>

<script>
// Handle material type selection
document.addEventListener('DOMContentLoaded', function() {
    const typeRadios = document.querySelectorAll('input[name="type"]');
    const sections = document.querySelectorAll('.material-section');
    
    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Hide all sections
            sections.forEach(section => section.classList.add('hidden'));
            
            // Show selected section
            const selectedSection = document.getElementById(this.value + '_section');
            if (selectedSection) {
                selectedSection.classList.remove('hidden');
            }
            
            // Update label styles
            document.querySelectorAll('.material-type-option').forEach(label => {
                label.classList.remove('border-purple-500', 'bg-purple-50');
                label.classList.add('border-gray-200');
            });
            
            this.nextElementSibling.classList.remove('border-gray-200');
            this.nextElementSibling.classList.add('border-purple-500', 'bg-purple-50');
        });
    });
    
    // Initialize on page load
    const checkedRadio = document.querySelector('input[name="type"]:checked');
    if (checkedRadio) {
        checkedRadio.dispatchEvent(new Event('change'));
    }
});

function updateFileName(input) {
    const fileNameDiv = document.getElementById('file-name');
    if (input.files.length > 0) {
        fileNameDiv.textContent = 'File dipilih: ' + input.files[0].name;
        fileNameDiv.classList.remove('hidden');
    } else {
        fileNameDiv.classList.add('hidden');
    }
}
</script>
@endsection
