<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Schedule;
use App\Models\Subject;
use App\Models\User;

class ScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Schedule::with(['subject', 'dosen']);

        // Filter by user's department
        if (Auth::user()->isMahasiswa()) {
            $query->whereHas('subject', function($q) {
                $q->where('jurusan', Auth::user()->jurusan);
            });
        } else {
            // <PERSON><PERSON> can see all schedules in their department
            $query->whereHas('subject', function($q) {
                $q->where('jurusan', Auth::user()->jurusan);
            });
        }

        // Filter by day
        if ($request->has('hari') && $request->hari) {
            $query->where('hari', $request->hari);
        }

        // Filter by subject
        if ($request->has('subject_id') && $request->subject_id) {
            $query->where('subject_id', $request->subject_id);
        }

        $schedules = $query->orderBy('hari')
                          ->orderBy('jam_mulai')
                          ->get()
                          ->groupBy('hari');

        $subjects = Subject::where('jurusan', Auth::user()->jurusan)->get();
        $days = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];

        return view('schedules.index', compact('schedules', 'subjects', 'days'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Only dosen can create schedules
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $subjects = Subject::where('jurusan', Auth::user()->jurusan)->get();
        $dosens = User::where('role', 'dosen')
                     ->where('jurusan', Auth::user()->jurusan)
                     ->get();
        $days = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];

        return view('schedules.create', compact('subjects', 'dosens', 'days'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Only dosen can store schedules
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'dosen_id' => 'required|exists:users,id',
            'ruang' => 'required|string|max:255',
            'hari' => 'required|in:Senin,Selasa,Rabu,Kamis,Jumat,Sabtu,Minggu',
            'jam_mulai' => 'required|date_format:H:i',
            'jam_selesai' => 'required|date_format:H:i|after:jam_mulai',
        ]);

        // Check for schedule conflicts
        $conflict = Schedule::where('dosen_id', $request->dosen_id)
                           ->where('hari', $request->hari)
                           ->where(function($query) use ($request) {
                               $query->whereBetween('jam_mulai', [$request->jam_mulai, $request->jam_selesai])
                                     ->orWhereBetween('jam_selesai', [$request->jam_mulai, $request->jam_selesai])
                                     ->orWhere(function($q) use ($request) {
                                         $q->where('jam_mulai', '<=', $request->jam_mulai)
                                           ->where('jam_selesai', '>=', $request->jam_selesai);
                                     });
                           })
                           ->exists();

        if ($conflict) {
            return back()->withErrors(['jam_mulai' => 'Jadwal bertabrakan dengan jadwal yang sudah ada.'])
                        ->withInput();
        }

        Schedule::create($request->all());

        return redirect()->route('schedules.index')
            ->with('success', 'Jadwal berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Schedule $schedule)
    {
        $schedule->load(['subject', 'dosen']);
        return view('schedules.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Schedule $schedule)
    {
        // Only dosen can edit schedules
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $subjects = Subject::where('jurusan', Auth::user()->jurusan)->get();
        $dosens = User::where('role', 'dosen')
                     ->where('jurusan', Auth::user()->jurusan)
                     ->get();
        $days = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];

        return view('schedules.edit', compact('schedule', 'subjects', 'dosens', 'days'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Schedule $schedule)
    {
        // Only dosen can update schedules
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'dosen_id' => 'required|exists:users,id',
            'ruang' => 'required|string|max:255',
            'hari' => 'required|in:Senin,Selasa,Rabu,Kamis,Jumat,Sabtu,Minggu',
            'jam_mulai' => 'required|date_format:H:i',
            'jam_selesai' => 'required|date_format:H:i|after:jam_mulai',
        ]);

        // Check for schedule conflicts (excluding current schedule)
        $conflict = Schedule::where('dosen_id', $request->dosen_id)
                           ->where('hari', $request->hari)
                           ->where('id', '!=', $schedule->id)
                           ->where(function($query) use ($request) {
                               $query->whereBetween('jam_mulai', [$request->jam_mulai, $request->jam_selesai])
                                     ->orWhereBetween('jam_selesai', [$request->jam_mulai, $request->jam_selesai])
                                     ->orWhere(function($q) use ($request) {
                                         $q->where('jam_mulai', '<=', $request->jam_mulai)
                                           ->where('jam_selesai', '>=', $request->jam_selesai);
                                     });
                           })
                           ->exists();

        if ($conflict) {
            return back()->withErrors(['jam_mulai' => 'Jadwal bertabrakan dengan jadwal yang sudah ada.'])
                        ->withInput();
        }

        $schedule->update($request->all());

        return redirect()->route('schedules.index')
            ->with('success', 'Jadwal berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Schedule $schedule)
    {
        // Only dosen can delete schedules
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $schedule->delete();

        return redirect()->route('schedules.index')
            ->with('success', 'Jadwal berhasil dihapus!');
    }
}
