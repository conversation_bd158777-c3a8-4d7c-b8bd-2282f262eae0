@extends('layouts.dashboard')

@section('title', $material->title)
@section('page-title', 'Detail Materi')

@section('content')
<div class="max-w-6xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mr-3
                        @if($material->type === 'pdf') bg-red-100 text-red-800
                        @elseif($material->type === 'link') bg-blue-100 text-blue-800
                        @else bg-gray-100 text-gray-800 @endif">
                        @if($material->type === 'pdf')
                            <i class="fas fa-file-pdf mr-1"></i>
                        @elseif($material->type === 'link')
                            <i class="fas fa-link mr-1"></i>
                        @else
                            <i class="fas fa-file-alt mr-1"></i>
                        @endif
                        {{ strtoupper($material->type) }}
                    </span>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $material->title }}</h1>
                <p class="text-gray-600 mt-1">{{ $material->subject->nama_mk }} - {{ $material->subject->kode_mk }}</p>
            </div>
            <div class="flex items-center space-x-3">
                @if(auth()->user()->id === $material->uploaded_by)
                <a href="{{ route('materials.edit', $material) }}" 
                   class="text-blue-600 hover:text-blue-700 font-medium">
                    <i class="fas fa-edit mr-1"></i>
                    Edit
                </a>
                <form method="POST" action="{{ route('materials.destroy', $material) }}" 
                      onsubmit="return confirm('Yakin ingin menghapus materi ini?')" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="text-red-600 hover:text-red-700 font-medium">
                        <i class="fas fa-trash mr-1"></i>
                        Hapus
                    </button>
                </form>
                @endif
                <a href="{{ route('materials.index') }}" 
                   class="text-gray-600 hover:text-gray-800 font-medium">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Material Content -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-book-open text-purple-600 mr-2"></i>
                        Konten Materi
                    </h2>
                </div>
                <div class="p-6">
                    @if($material->type === 'text')
                        <div class="prose max-w-none">
                            {!! nl2br(e($material->content)) !!}
                        </div>
                    @elseif($material->type === 'pdf')
                        <div class="text-center">
                            <div class="bg-red-50 rounded-lg p-8 mb-6">
                                <i class="fas fa-file-pdf text-red-600 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $material->file_name }}</h3>
                                <p class="text-gray-600 mb-4">
                                    Ukuran: {{ number_format($material->file_size / 1024, 2) }} KB
                                </p>
                                <div class="space-x-4">
                                    <a href="{{ $material->getFileUrl() }}" target="_blank"
                                       class="btn-gradient text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                                        <i class="fas fa-eye mr-2"></i>
                                        Lihat PDF
                                    </a>
                                    <a href="{{ $material->getFileUrl() }}" download
                                       class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center transition duration-200">
                                        <i class="fas fa-download mr-2"></i>
                                        Download
                                    </a>
                                </div>
                            </div>
                            <!-- PDF Viewer -->
                            <div class="border border-gray-300 rounded-lg overflow-hidden">
                                <iframe src="{{ $material->getFileUrl() }}" 
                                        class="w-full h-96" 
                                        frameborder="0">
                                    Browser Anda tidak mendukung PDF viewer. 
                                    <a href="{{ $material->getFileUrl() }}" target="_blank">Klik di sini untuk membuka PDF</a>
                                </iframe>
                            </div>
                        </div>
                    @elseif($material->type === 'link')
                        <div class="text-center">
                            <div class="bg-blue-50 rounded-lg p-8">
                                <i class="fas fa-external-link-alt text-blue-600 text-4xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Link Eksternal</h3>
                                <p class="text-gray-600 mb-4">Klik tombol di bawah untuk mengakses materi</p>
                                <a href="{{ $material->link_url }}" target="_blank" rel="noopener noreferrer"
                                   class="btn-gradient text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    Buka Link
                                </a>
                                <p class="text-xs text-gray-500 mt-4">{{ $material->link_url }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Comments Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-comments text-purple-600 mr-2"></i>
                        Diskusi & Komentar ({{ $material->comments->where('parent_id', null)->count() }})
                    </h2>
                </div>
                
                <!-- Add Comment Form -->
                <div class="p-6 border-b border-gray-200 bg-gray-50">
                    <form method="POST" action="{{ route('comments.store', $material) }}">
                        @csrf
                        <div class="flex items-start space-x-4">
                            <div class="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <textarea name="content" rows="3" required
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                          placeholder="Tulis komentar atau pertanyaan Anda..."></textarea>
                                <div class="mt-3 flex justify-end">
                                    <button type="submit" 
                                            class="btn-gradient text-white px-6 py-2 rounded-lg font-medium">
                                        <i class="fas fa-paper-plane mr-2"></i>
                                        Kirim Komentar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Comments List -->
                <div class="p-6">
                    @if($material->comments->where('parent_id', null)->count() > 0)
                        <div class="space-y-6">
                            @foreach($material->comments->where('parent_id', null) as $comment)
                            <div class="comment-item">
                                <div class="flex items-start space-x-4">
                                    <div class="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600 text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="bg-gray-50 rounded-lg p-4">
                                            <div class="flex items-center justify-between mb-2">
                                                <div class="flex items-center space-x-2">
                                                    <span class="font-medium text-gray-900">{{ $comment->user->name }}</span>
                                                    @if($comment->user->role === 'dosen')
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                                            Dosen
                                                        </span>
                                                    @endif
                                                </div>
                                                <span class="text-sm text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                                            </div>
                                            <p class="text-gray-700">{{ $comment->content }}</p>
                                        </div>
                                        
                                        <!-- Replies -->
                                        @if($comment->replies->count() > 0)
                                        <div class="mt-4 ml-6 space-y-3">
                                            @foreach($comment->replies as $reply)
                                            <div class="flex items-start space-x-3">
                                                <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600 text-xs"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3">
                                                        <div class="flex items-center justify-between mb-1">
                                                            <div class="flex items-center space-x-2">
                                                                <span class="font-medium text-gray-900 text-sm">{{ $reply->user->name }}</span>
                                                                @if($reply->user->role === 'dosen')
                                                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                                                        Dosen
                                                                    </span>
                                                                @endif
                                                            </div>
                                                            <span class="text-xs text-gray-500">{{ $reply->created_at->diffForHumans() }}</span>
                                                        </div>
                                                        <p class="text-gray-700 text-sm">{{ $reply->content }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                        @endif

                                        <!-- Reply Form -->
                                        <div class="mt-3 ml-6">
                                            <button onclick="toggleReplyForm({{ $comment->id }})" 
                                                    class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                                <i class="fas fa-reply mr-1"></i>
                                                Balas
                                            </button>
                                            <div id="reply-form-{{ $comment->id }}" class="hidden mt-3">
                                                <form method="POST" action="{{ route('comments.store', $material) }}">
                                                    @csrf
                                                    <input type="hidden" name="parent_id" value="{{ $comment->id }}">
                                                    <div class="flex items-start space-x-3">
                                                        <div class="h-8 w-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                                            <i class="fas fa-user text-white text-xs"></i>
                                                        </div>
                                                        <div class="flex-1">
                                                            <textarea name="content" rows="2" required
                                                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                                                                      placeholder="Tulis balasan..."></textarea>
                                                            <div class="mt-2 flex justify-end space-x-2">
                                                                <button type="button" onclick="toggleReplyForm({{ $comment->id }})"
                                                                        class="px-3 py-1 text-gray-600 hover:text-gray-800 text-sm">
                                                                    Batal
                                                                </button>
                                                                <button type="submit" 
                                                                        class="btn-gradient text-white px-4 py-1 rounded text-sm">
                                                                    Balas
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-comments text-gray-400 text-3xl mb-4"></i>
                            <p class="text-gray-500">Belum ada komentar. Jadilah yang pertama berkomentar!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Material Info -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-info-circle text-purple-600 mr-2"></i>
                        Informasi Materi
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Mata Kuliah</label>
                        <p class="text-gray-900">{{ $material->subject->nama_mk }}</p>
                        <p class="text-sm text-gray-600">{{ $material->subject->kode_mk }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Dosen Pengampu</label>
                        <p class="text-gray-900">{{ $material->uploader->name }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Diupload</label>
                        <p class="text-gray-900">{{ $material->created_at->format('d M Y, H:i') }}</p>
                        <p class="text-sm text-gray-600">{{ $material->created_at->diffForHumans() }}</p>
                    </div>
                    @if($material->updated_at != $material->created_at)
                    <div>
                        <label class="text-sm font-medium text-gray-500">Terakhir Diperbarui</label>
                        <p class="text-gray-900">{{ $material->updated_at->format('d M Y, H:i') }}</p>
                        <p class="text-sm text-gray-600">{{ $material->updated_at->diffForHumans() }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Description -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-align-left text-purple-600 mr-2"></i>
                        Deskripsi
                    </h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-700">{{ $material->description }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleReplyForm(commentId) {
    const form = document.getElementById('reply-form-' + commentId);
    form.classList.toggle('hidden');
}
</script>

@if(session('success'))
<div id="success-alert" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
    <div class="flex items-center">
        <i class="fas fa-check-circle mr-2"></i>
        {{ session('success') }}
    </div>
</div>

<script>
    setTimeout(function() {
        const alert = document.getElementById('success-alert');
        if (alert) {
            alert.style.opacity = '0';
            alert.style.transform = 'translateX(100%)';
            setTimeout(() => alert.remove(), 300);
        }
    }, 3000);
</script>
@endif
@endsection
