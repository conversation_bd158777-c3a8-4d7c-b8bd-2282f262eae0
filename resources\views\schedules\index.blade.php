@extends('layouts.dashboard')

@section('title', '<PERSON><PERSON><PERSON>')
@section('page-title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="space-y-6">
    <!-- Header Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">J<PERSON><PERSON> Mata Kulia<PERSON></h1>
                <p class="text-gray-600 mt-1"><PERSON>hat dan kelola jadwal per<PERSON>lia<PERSON></p>
            </div>
            @if(auth()->user()->isDosen())
            <div class="mt-4 md:mt-0">
                <a href="{{ route('schedules.create') }}" 
                   class="btn-gradient text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    <PERSON><PERSON> Jadwal
                </a>
            </div>
            @endif
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <form method="GET" action="{{ route('schedules.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
            <!-- Day Filter -->
            <div class="md:w-48">
                <label for="hari" class="block text-sm font-medium text-gray-700 mb-2">Hari</label>
                <select name="hari" id="hari" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="">Semua Hari</option>
                    @foreach($days as $day)
                        <option value="{{ $day }}" {{ request('hari') == $day ? 'selected' : '' }}>
                            {{ $day }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Subject Filter -->
            <div class="md:w-64">
                <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-2">Mata Kuliah</label>
                <select name="subject_id" id="subject_id" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="">Semua Mata Kuliah</option>
                    @foreach($subjects as $subject)
                        <option value="{{ $subject->id }}" {{ request('subject_id') == $subject->id ? 'selected' : '' }}>
                            {{ $subject->nama_mk }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Filter Button -->
            <div>
                <button type="submit" 
                        class="w-full md:w-auto px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>
            </div>

            <!-- Reset Button -->
            @if(request()->hasAny(['hari', 'subject_id']))
            <div>
                <a href="{{ route('schedules.index') }}" 
                   class="w-full md:w-auto px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition duration-200 inline-block text-center">
                    <i class="fas fa-times mr-2"></i>
                    Reset
                </a>
            </div>
            @endif
        </form>
    </div>

    <!-- Schedule Table -->
    @if($schedules->count() > 0)
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            @foreach($days as $day)
                @if(isset($schedules[$day]) && $schedules[$day]->count() > 0)
                <div class="border-b border-gray-200 last:border-b-0">
                    <!-- Day Header -->
                    <div class="bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-4">
                        <h3 class="text-lg font-semibold text-white">
                            <i class="fas fa-calendar-day mr-2"></i>
                            {{ $day }}
                        </h3>
                    </div>
                    
                    <!-- Schedule Items -->
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            @foreach($schedules[$day] as $schedule)
                            <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition duration-200">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <div class="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-book text-purple-600"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-900">{{ $schedule->subject->nama_mk }}</h4>
                                                <p class="text-sm text-gray-600">{{ $schedule->subject->kode_mk }}</p>
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-2 text-sm text-gray-600">
                                            <div class="flex items-center">
                                                <i class="fas fa-user-tie w-4 text-purple-500 mr-2"></i>
                                                <span>{{ $schedule->dosen->name }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-clock w-4 text-purple-500 mr-2"></i>
                                                <span>{{ $schedule->jam_mulai }} - {{ $schedule->jam_selesai }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-map-marker-alt w-4 text-purple-500 mr-2"></i>
                                                <span>{{ $schedule->ruang }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-graduation-cap w-4 text-purple-500 mr-2"></i>
                                                <span>{{ $schedule->subject->sks }} SKS</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    @if(auth()->user()->isDosen())
                                    <div class="flex items-center space-x-2 ml-4">
                                        <a href="{{ route('schedules.edit', $schedule) }}" 
                                           class="text-blue-600 hover:text-blue-700 p-2">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ route('schedules.destroy', $schedule) }}" 
                                              onsubmit="return confirm('Yakin ingin menghapus jadwal ini?')" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-700 p-2">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            @endforeach
        </div>
    </div>
    @else
    <!-- Empty State -->
    <div class="bg-white rounded-xl shadow-sm p-12 border border-gray-200 text-center">
        <div class="max-w-md mx-auto">
            <div class="h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-calendar-times text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Jadwal</h3>
            <p class="text-gray-600 mb-6">
                @if(request()->hasAny(['hari', 'subject_id']))
                    Tidak ada jadwal yang sesuai dengan filter yang dipilih.
                @else
                    Belum ada jadwal yang tersedia untuk jurusan Anda.
                @endif
            </p>
            @if(auth()->user()->isDosen())
            <a href="{{ route('schedules.create') }}" 
               class="btn-gradient text-white px-6 py-3 rounded-lg font-medium inline-flex items-center">
                <i class="fas fa-plus mr-2"></i>
                Tambah Jadwal Pertama
            </a>
            @endif
        </div>
    </div>
    @endif

    <!-- Today's Schedule Highlight -->
    @php
        $today = now()->locale('id')->dayName;
        $todaySchedules = isset($schedules[$today]) ? $schedules[$today] : collect();
    @endphp
    
    @if($todaySchedules->count() > 0)
    <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-sm p-6 text-white">
        <div class="flex items-center mb-4">
            <div class="h-12 w-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar-day text-white text-xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold">Jadwal Hari Ini</h3>
                <p class="text-purple-100">{{ $today }}, {{ now()->format('d M Y') }}</p>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($todaySchedules as $schedule)
            <div class="bg-white bg-opacity-10 rounded-lg p-4">
                <h4 class="font-semibold mb-2">{{ $schedule->subject->nama_mk }}</h4>
                <div class="space-y-1 text-sm text-purple-100">
                    <div class="flex items-center">
                        <i class="fas fa-clock w-4 mr-2"></i>
                        <span>{{ $schedule->jam_mulai }} - {{ $schedule->jam_selesai }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt w-4 mr-2"></i>
                        <span>{{ $schedule->ruang }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-user-tie w-4 mr-2"></i>
                        <span>{{ $schedule->dosen->name }}</span>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>

@if(session('success'))
<div id="success-alert" class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
    <div class="flex items-center">
        <i class="fas fa-check-circle mr-2"></i>
        {{ session('success') }}
    </div>
</div>

<script>
    setTimeout(function() {
        const alert = document.getElementById('success-alert');
        if (alert) {
            alert.style.opacity = '0';
            alert.style.transform = 'translateX(100%)';
            setTimeout(() => alert.remove(), 300);
        }
    }, 3000);
</script>
@endif
@endsection
